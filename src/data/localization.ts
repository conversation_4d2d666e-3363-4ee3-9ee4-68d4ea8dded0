/**
 * Kenyan market localization configuration
 * Tailored for the Kenyan e-commerce market
 */

export interface LocalizationConfig {
  country: string;
  currency: string;
  currencySymbol: string;
  locale: string;
  timezone: string;
  phoneFormat: string;
  addressFormat: string[];
  businessHours: {
    weekdays: string;
    weekends: string;
    holidays: string;
  };
  supportedLanguages: string[];
  paymentMethods: string[];
  shippingRegions: string[];
}

export const KENYA_LOCALIZATION: LocalizationConfig = {
  country: 'Kenya',
  currency: 'KES',
  currencySymbol: 'KSh',
  locale: 'en-KE',
  timezone: 'Africa/Nairobi',
  phoneFormat: '+254 XXX XXX XXX',
  addressFormat: [
    'Full Name',
    'Phone Number',
    'Street Address / Building Name',
    'Area / Estate',
    'City / Town',
    'County',
    'Postal Code (Optional)'
  ],
  businessHours: {
    weekdays: '8:00 AM - 6:00 PM (Monday - Friday)',
    weekends: '9:00 AM - 5:00 PM (Saturday)',
    holidays: 'Closed on Sundays and Public Holidays'
  },
  supportedLanguages: ['English', 'Kiswahili'],
  paymentMethods: ['M-Pesa', 'Airtel Money', 'Bank Transfer', 'Card Payment', 'Cash on Delivery'],
  shippingRegions: [
    'Nairobi County',
    'Kiambu County', 
    'Central Kenya',
    'Coast Province',
    'Western Kenya',
    'Eastern Kenya',
    'Northern Kenya',
    'Rift Valley'
  ]
};

/**
 * Kenyan market-specific content and messaging
 */
export const KENYAN_CONTENT = {
  welcome: {
    title: 'Welcome to Bedazzled Kenya',
    subtitle: 'Premium rhinestone portraits crafted by local Kenyan artisans',
    description: 'Transform your precious memories into stunning bedazzled masterpieces. We serve customers across Kenya with fast, reliable delivery.'
  },
  
  features: [
    {
      title: 'Local Artisans',
      description: 'Supporting talented Kenyan artists and craftspeople',
      icon: '🎨'
    },
    {
      title: 'M-Pesa Payments',
      description: 'Pay securely with M-Pesa, Airtel Money, or bank transfer',
      icon: '📱'
    },
    {
      title: 'Nairobi Delivery',
      description: 'Free same-day delivery within Nairobi CBD',
      icon: '🚚'
    },
    {
      title: 'Quality Guarantee',
      description: '100% satisfaction guarantee on all our products',
      icon: '✨'
    }
  ],

  pricing: {
    startingFrom: 'Starting from',
    freeShipping: 'Free shipping',
    freeShippingThreshold: 15000, // KES 15,000
    currency: 'KES',
    taxIncluded: 'VAT included',
    installments: 'Pay in installments available'
  },

  shipping: {
    freeDeliveryAreas: [
      'Nairobi CBD',
      'Westlands',
      'Kilimani',
      'Karen',
      'Runda',
      'Lavington'
    ],
    expressDelivery: 'Same-day delivery available in Nairobi',
    standardDelivery: 'Standard delivery 2-7 days nationwide',
    trackingAvailable: 'SMS tracking updates included'
  },

  support: {
    phone: '+*********** 456',
    whatsapp: '+*********** 456',
    email: '<EMAIL>',
    hours: 'Monday - Saturday, 8 AM - 6 PM EAT',
    languages: ['English', 'Kiswahili']
  },

  legal: {
    businessName: 'Bedazzled Kenya Limited',
    registrationNumber: 'PVT-XXXXXXXXX/2024',
    vatNumber: 'P051XXXXXXX',
    address: [
      'Bedazzled Kenya Limited',
      'Westlands Square, 2nd Floor',
      'Ring Road Parklands',
      'P.O. Box 12345-00100',
      'Nairobi, Kenya'
    ]
  },

  social: {
    facebook: '@BedazzledKenya',
    instagram: '@bedazzled_kenya',
    twitter: '@BedazzledKE',
    tiktok: '@bedazzledkenya',
    youtube: 'Bedazzled Kenya'
  }
};

/**
 * Kenyan holidays and special dates
 */
export const KENYAN_HOLIDAYS = [
  { date: '2024-01-01', name: 'New Year\'s Day' },
  { date: '2024-04-01', name: 'Easter Monday' },
  { date: '2024-05-01', name: 'Labour Day' },
  { date: '2024-06-01', name: 'Madaraka Day' },
  { date: '2024-10-10', name: 'Huduma Day' },
  { date: '2024-10-20', name: 'Mashujaa Day' },
  { date: '2024-12-12', name: 'Jamhuri Day' },
  { date: '2024-12-25', name: 'Christmas Day' },
  { date: '2024-12-26', name: 'Boxing Day' }
];

/**
 * Format phone number for Kenya
 */
export function formatKenyanPhone(phone: string): string {
  // Remove any non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Handle different formats
  if (digits.startsWith('254')) {
    // Already has country code
    return `+${digits}`;
  } else if (digits.startsWith('0')) {
    // Local format starting with 0
    return `+254${digits.substring(1)}`;
  } else if (digits.length === 9) {
    // 9 digits without leading 0
    return `+254${digits}`;
  }
  
  return phone; // Return original if format not recognized
}

/**
 * Validate Kenyan phone number
 */
export function isValidKenyanPhone(phone: string): boolean {
  const formatted = formatKenyanPhone(phone);
  const kenyanPhoneRegex = /^\+254[17]\d{8}$/;
  return kenyanPhoneRegex.test(formatted);
}

/**
 * Get business hours status
 */
export function getBusinessHoursStatus(): {
  isOpen: boolean;
  message: string;
  nextOpenTime?: string;
} {
  const now = new Date();
  const nairobiTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Nairobi"}));
  const day = nairobiTime.getDay(); // 0 = Sunday, 6 = Saturday
  const hour = nairobiTime.getHours();
  
  // Check if it's a weekday (Monday-Friday)
  if (day >= 1 && day <= 5) {
    if (hour >= 8 && hour < 18) {
      return {
        isOpen: true,
        message: 'We\'re open! Contact us for immediate assistance.'
      };
    }
  }
  
  // Check if it's Saturday
  if (day === 6) {
    if (hour >= 9 && hour < 17) {
      return {
        isOpen: true,
        message: 'We\'re open! Saturday hours: 9 AM - 5 PM.'
      };
    }
  }
  
  // Closed
  if (day === 0) {
    return {
      isOpen: false,
      message: 'We\'re closed on Sundays. We\'ll be back Monday at 8 AM.',
      nextOpenTime: 'Monday 8:00 AM'
    };
  }
  
  if (day >= 1 && day <= 5) {
    return {
      isOpen: false,
      message: 'We\'re currently closed. Business hours: 8 AM - 6 PM.',
      nextOpenTime: hour < 8 ? 'Today 8:00 AM' : 'Tomorrow 8:00 AM'
    };
  }
  
  return {
    isOpen: false,
    message: 'We\'re currently closed. We\'ll be back Monday at 8 AM.',
    nextOpenTime: 'Monday 8:00 AM'
  };
}

/**
 * Check if today is a Kenyan holiday
 */
export function isKenyanHoliday(date: Date = new Date()): boolean {
  const dateString = date.toISOString().split('T')[0];
  return KENYAN_HOLIDAYS.some(holiday => holiday.date === dateString);
}

/**
 * Get appropriate greeting based on time of day
 */
export function getTimeBasedGreeting(): string {
  const now = new Date();
  const nairobiTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Nairobi"}));
  const hour = nairobiTime.getHours();
  
  if (hour < 12) {
    return 'Good morning';
  } else if (hour < 17) {
    return 'Good afternoon';
  } else {
    return 'Good evening';
  }
}
